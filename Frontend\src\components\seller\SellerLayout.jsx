import React, { useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import { useLocation } from "react-router-dom";
import {
  selectActiveTab,
  selectIsSidebarOpen,
  setActiveTab,
  setSidebarOpen,
  toggleSidebar,
} from "../../redux/slices/sellerDashboardSlice";
import SellerSidebar from "./SellerSidebar";
import "../../styles/SellerLayout.css";

// Icons
import { FaBars } from "react-icons/fa";
import { FaTimes } from "react-icons/fa";

const SellerLayout = ({ children, title, subtitle }) => {
  const dispatch = useDispatch();
  const location = useLocation();
  const activeTab = useSelector(selectActiveTab);
  const isSidebarOpen = useSelector(selectIsSidebarOpen);

  // Map routes to tabs
  const routeToTabMap = {
    "/seller/dashboard": "dashboard",
    "/seller/profile": "profile",
    "/seller/upload": "upload",
    "/seller/my-content": "my-content",
    "/seller/analytics": "analytics",
    "/seller/orders": "orders",
    "/seller/settings": "settings",
  };

  // Update active tab based on current route
  useEffect(() => {
    const currentTab = routeToTabMap[location.pathname];
    if (currentTab && currentTab !== activeTab) {
      dispatch(setActiveTab(currentTab));
    }
  }, [location.pathname, activeTab, dispatch]);

  // Handle sidebar toggle
  const handleSidebarToggle = () => {
    dispatch(toggleSidebar());
  };

  // Handle sidebar close (for mobile overlay)
  const handleSidebarClose = () => {
    dispatch(setSidebarOpen(false));
  };

  // Close sidebar on route change (mobile)
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth > 767) {
        dispatch(setSidebarOpen(false));
      }
    };

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, [dispatch]);

  // Close sidebar when clicking outside (mobile)
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        isSidebarOpen &&
        window.innerWidth <= 767 &&
        !event.target.closest(".SellerSidebar") &&
        !event.target.closest(".SellerLayout__toggleBtn")
      ) {
        dispatch(setSidebarOpen(false));
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, [isSidebarOpen, dispatch]);

  return (
    <div className="SellerLayout">
      {/* Mobile Sidebar Overlay */}
      {isSidebarOpen && (
        <div
          className="SellerLayout__overlay"
          onClick={handleSidebarClose}
        />
      )}

      {/* Sidebar */}
      <div className={`SellerLayout__sidebar ${isSidebarOpen ? "SellerLayout__sidebar--open" : ""}`}>
        <SellerSidebar />
      </div>

      {/* Main Content */}
      <div className="SellerLayout__main">
        {/* Header */}
        <header className="SellerLayout__header">
          <div className="SellerLayout__headerContent">
            {/* Mobile Menu Toggle */}
            <button
              className="SellerLayout__toggleBtn"
              onClick={handleSidebarToggle}
              aria-label="Toggle sidebar"
            >
              {isSidebarOpen ? <FaTimes /> : <FaBars />}
            </button>

            {/* Page Title */}
            <div className="SellerLayout__titleSection">
              {title && <h1 className="SellerLayout__title">{title}</h1>}
              {subtitle && <p className="SellerLayout__subtitle">{subtitle}</p>}
            </div>

            {/* Header Actions (can be extended) */}
            <div className="SellerLayout__headerActions">
              {/* Add notification bell, user menu, etc. here */}
            </div>
          </div>
        </header>

        {/* Content Area */}
        <main className="SellerLayout__content">
          <div className="SellerLayout__contentWrapper">
            {children}
          </div>
        </main>
      </div>
    </div>
  );
};

export default SellerLayout;
