import React from 'react';
import { Navigate } from 'react-router-dom';
import { useSelector } from 'react-redux';
import authService from '../../services/authService';

/**
 * ProtectedRoute component that prevents access based on authentication status
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - Child components to render
 * @param {boolean} props.requireAuth - If true, requires authentication to access
 * @param {boolean} props.preventAuth - If true, prevents authenticated users from accessing
 * @param {string} props.redirectTo - Path to redirect to when access is denied
 * @param {string|Array} props.allowedRoles - Roles allowed to access this route
 */
const ProtectedRoute = ({ 
  children, 
  requireAuth = false, 
  preventAuth = false, 
  redirectTo = '/', 
  allowedRoles = null 
}) => {
  const { user, isAuthenticated } = useSelector((state) => state.auth);
  
  // Check if user is authenticated (from Redux or localStorage)
  const isUserAuthenticated = isAuthenticated || authService.isAuthenticated();
  const userData = user || authService.getStoredUser();

  // If route prevents authenticated users and user is authenticated, redirect
  if (preventAuth && isUserAuthenticated) {
    // Redirect based on user role
    if (userData?.role === 'seller') {
      return <Navigate to="/seller/dashboard" replace />;
    } else if (userData?.role === 'admin') {
      return <Navigate to="/admin/dashboard" replace />;
    } else {
      return <Navigate to="/buyer/dashboard" replace />;
    }
  }

  // If route requires authentication and user is not authenticated, redirect
  if (requireAuth && !isUserAuthenticated) {
    return <Navigate to="/auth" replace />;
  }

  // If specific roles are required, check user role
  if (allowedRoles && userData) {
    const userRole = userData.role;
    const rolesArray = Array.isArray(allowedRoles) ? allowedRoles : [allowedRoles];
    
    if (!rolesArray.includes(userRole)) {
      // Redirect to appropriate dashboard based on user's actual role
      if (userRole === 'seller') {
        return <Navigate to="/seller/dashboard" replace />;
      } else if (userRole === 'admin') {
        return <Navigate to="/admin/dashboard" replace />;
      } else {
        return <Navigate to="/buyer/dashboard" replace />;
      }
    }
  }

  // If all checks pass, render the children
  return children;
};

export default ProtectedRoute;
