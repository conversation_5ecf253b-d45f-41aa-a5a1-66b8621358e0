import React from "react";
import { useSelector } from "react-redux";
import {
  selectStats,
  selectRecentActivities,
  selectAnalytics,
} from "../../redux/slices/sellerDashboardSlice";
import SellerLayout from "../../components/seller/SellerLayout";
import "../../styles/SellerDashboard.css";

// Icons
import { FaDollarSign, FaShoppingCart, FaFileAlt, FaChartLine } from "react-icons/fa";
import { MdTrendingUp, MdTrendingDown } from "react-icons/md";

const SellerDashboard = () => {
  const stats = useSelector(selectStats);
  const recentActivities = useSelector(selectRecentActivities);
  const analytics = useSelector(selectAnalytics);

  // Format currency
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  // Format date
  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  // Get activity icon
  const getActivityIcon = (type) => {
    switch (type) {
      case 'sale':
        return <FaDollarSign className="SellerDashboard__activityIcon SellerDashboard__activityIcon--sale" />;
      case 'upload':
        return <FaFileAlt className="SellerDashboard__activityIcon SellerDashboard__activityIcon--upload" />;
      case 'review':
        return <FaChartLine className="SellerDashboard__activityIcon SellerDashboard__activityIcon--review" />;
      default:
        return <FaFileAlt className="SellerDashboard__activityIcon" />;
    }
  };

  return (
    <SellerLayout
      title="Dashboard"
      subtitle="Welcome back! Here's what's happening with your content."
    >
      <div className="SellerDashboard">
        {/* Stats Cards */}
        <div className="SellerDashboard__statsGrid">
          <div className="SellerDashboard__statCard">
            <div className="SellerDashboard__statIcon SellerDashboard__statIcon--revenue">
              <FaDollarSign />
            </div>
            <div className="SellerDashboard__statContent">
              <h3 className="SellerDashboard__statValue">{formatCurrency(stats.totalRevenue)}</h3>
              <p className="SellerDashboard__statLabel">Total Revenue</p>
              <div className="SellerDashboard__statTrend SellerDashboard__statTrend--up">
                <MdTrendingUp />
                <span>+12.5% from last month</span>
              </div>
            </div>
          </div>

          <div className="SellerDashboard__statCard">
            <div className="SellerDashboard__statIcon SellerDashboard__statIcon--sales">
              <FaShoppingCart />
            </div>
            <div className="SellerDashboard__statContent">
              <h3 className="SellerDashboard__statValue">{stats.totalSales}</h3>
              <p className="SellerDashboard__statLabel">Total Sales</p>
              <div className="SellerDashboard__statTrend SellerDashboard__statTrend--up">
                <MdTrendingUp />
                <span>+8.2% from last month</span>
              </div>
            </div>
          </div>

          <div className="SellerDashboard__statCard">
            <div className="SellerDashboard__statIcon SellerDashboard__statIcon--content">
              <FaFileAlt />
            </div>
            <div className="SellerDashboard__statContent">
              <h3 className="SellerDashboard__statValue">{stats.totalContent}</h3>
              <p className="SellerDashboard__statLabel">Total Content</p>
              <div className="SellerDashboard__statTrend SellerDashboard__statTrend--neutral">
                <span>{stats.activeContent} active, {stats.draftContent} drafts</span>
              </div>
            </div>
          </div>

          <div className="SellerDashboard__statCard">
            <div className="SellerDashboard__statIcon SellerDashboard__statIcon--orders">
              <FaChartLine />
            </div>
            <div className="SellerDashboard__statContent">
              <h3 className="SellerDashboard__statValue">{stats.pendingOrders}</h3>
              <p className="SellerDashboard__statLabel">Pending Orders</p>
              <div className="SellerDashboard__statTrend SellerDashboard__statTrend--down">
                <MdTrendingDown />
                <span>-3 from yesterday</span>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content Grid */}
        <div className="SellerDashboard__mainGrid">
          {/* Recent Activities */}
          <div className="SellerDashboard__section">
            <div className="SellerDashboard__sectionHeader">
              <h2 className="SellerDashboard__sectionTitle">Recent Activities</h2>
              <button className="SellerDashboard__sectionAction">View All</button>
            </div>
            <div className="SellerDashboard__activitiesList">
              {recentActivities.map((activity) => (
                <div key={activity.id} className="SellerDashboard__activityItem">
                  {getActivityIcon(activity.type)}
                  <div className="SellerDashboard__activityContent">
                    <h4 className="SellerDashboard__activityTitle">{activity.title}</h4>
                    {activity.amount && (
                      <p className="SellerDashboard__activityAmount">
                        {formatCurrency(activity.amount)}
                      </p>
                    )}
                    {activity.buyer && (
                      <p className="SellerDashboard__activityBuyer">Buyer: {activity.buyer}</p>
                    )}
                  </div>
                  <div className="SellerDashboard__activityTime">
                    {formatDate(activity.timestamp)}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Top Performing Content */}
          <div className="SellerDashboard__section">
            <div className="SellerDashboard__sectionHeader">
              <h2 className="SellerDashboard__sectionTitle">Top Performing Content</h2>
              <button className="SellerDashboard__sectionAction">View All</button>
            </div>
            <div className="SellerDashboard__topContentList">
              {analytics.topContent.map((content, index) => (
                <div key={index} className="SellerDashboard__topContentItem">
                  <div className="SellerDashboard__topContentRank">#{index + 1}</div>
                  <div className="SellerDashboard__topContentInfo">
                    <h4 className="SellerDashboard__topContentTitle">{content.title}</h4>
                    <div className="SellerDashboard__topContentStats">
                      <span className="SellerDashboard__topContentSales">{content.sales} sales</span>
                      <span className="SellerDashboard__topContentRevenue">
                        {formatCurrency(content.revenue)}
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="SellerDashboard__quickActions">
          <h2 className="SellerDashboard__sectionTitle">Quick Actions</h2>
          <div className="SellerDashboard__actionGrid">
            <button className="SellerDashboard__actionBtn SellerDashboard__actionBtn--primary">
              <FaFileAlt />
              <span>Upload New Content</span>
            </button>
            <button className="SellerDashboard__actionBtn SellerDashboard__actionBtn--secondary">
              <FaChartLine />
              <span>View Analytics</span>
            </button>
            <button className="SellerDashboard__actionBtn SellerDashboard__actionBtn--secondary">
              <FaShoppingCart />
              <span>Manage Orders</span>
            </button>
          </div>
        </div>
      </div>
    </SellerLayout>
  );
};

export default SellerDashboard;
