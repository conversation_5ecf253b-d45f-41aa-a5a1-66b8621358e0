import React, { useState } from "react";
import { useSelector } from "react-redux";
import { selectMyContent } from "../../redux/slices/sellerDashboardSlice";
import SellerLayout from "../../components/seller/SellerLayout";
import "../../styles/SellerMyContent.css";

// Icons
import { FaEdit, FaTrash, FaEye, FaPlus, FaFilter, FaSearch } from "react-icons/fa";
import { MdGridView, MdViewList } from "react-icons/md";

const SellerMyContent = () => {
  const myContent = useSelector(selectMyContent);
  const [viewMode, setViewMode] = useState('grid'); // 'grid' or 'list'
  const [filterStatus, setFilterStatus] = useState('all'); // 'all', 'active', 'draft'
  const [searchTerm, setSearchTerm] = useState('');

  // Filter content based on status and search term
  const filteredContent = myContent.filter(content => {
    const matchesStatus = filterStatus === 'all' || content.status === filterStatus;
    const matchesSearch = content.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         content.category.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesStatus && matchesSearch;
  });

  // Format currency
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  // Format date
  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  // Get status badge class
  const getStatusBadgeClass = (status) => {
    switch (status) {
      case 'active':
        return 'SellerMyContent__statusBadge--active';
      case 'draft':
        return 'SellerMyContent__statusBadge--draft';
      default:
        return 'SellerMyContent__statusBadge--default';
    }
  };

  return (
    <SellerLayout
      title="My Content"
      subtitle="Manage your uploaded content and track performance"
    >
      <div className="SellerMyContent">
        {/* Header Controls */}
        <div className="SellerMyContent__header">
          <div className="SellerMyContent__headerLeft">
            <button className="SellerMyContent__addBtn">
              <FaPlus />
              <span>Upload New Content</span>
            </button>
          </div>

          <div className="SellerMyContent__headerRight">
            {/* Search */}
            <div className="SellerMyContent__searchBox">
              <FaSearch className="SellerMyContent__searchIcon" />
              <input
                type="text"
                placeholder="Search content..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="SellerMyContent__searchInput"
              />
            </div>

            {/* Filter */}
            <div className="SellerMyContent__filterBox">
              <FaFilter className="SellerMyContent__filterIcon" />
              <select
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value)}
                className="SellerMyContent__filterSelect"
              >
                <option value="all">All Status</option>
                <option value="active">Active</option>
                <option value="draft">Draft</option>
              </select>
            </div>

            {/* View Mode Toggle */}
            <div className="SellerMyContent__viewToggle">
              <button
                className={`SellerMyContent__viewBtn ${viewMode === 'grid' ? 'SellerMyContent__viewBtn--active' : ''}`}
                onClick={() => setViewMode('grid')}
                title="Grid View"
              >
                <MdGridView />
              </button>
              <button
                className={`SellerMyContent__viewBtn ${viewMode === 'list' ? 'SellerMyContent__viewBtn--active' : ''}`}
                onClick={() => setViewMode('list')}
                title="List View"
              >
                <MdViewList />
              </button>
            </div>
          </div>
        </div>

        {/* Content Stats */}
        <div className="SellerMyContent__stats">
          <div className="SellerMyContent__statItem">
            <span className="SellerMyContent__statValue">{myContent.length}</span>
            <span className="SellerMyContent__statLabel">Total Content</span>
          </div>
          <div className="SellerMyContent__statItem">
            <span className="SellerMyContent__statValue">
              {myContent.filter(c => c.status === 'active').length}
            </span>
            <span className="SellerMyContent__statLabel">Active</span>
          </div>
          <div className="SellerMyContent__statItem">
            <span className="SellerMyContent__statValue">
              {myContent.filter(c => c.status === 'draft').length}
            </span>
            <span className="SellerMyContent__statLabel">Drafts</span>
          </div>
          <div className="SellerMyContent__statItem">
            <span className="SellerMyContent__statValue">
              {formatCurrency(myContent.reduce((total, content) => total + content.revenue, 0))}
            </span>
            <span className="SellerMyContent__statLabel">Total Revenue</span>
          </div>
        </div>

        {/* Content List/Grid */}
        <div className={`SellerMyContent__content ${viewMode === 'grid' ? 'SellerMyContent__content--grid' : 'SellerMyContent__content--list'}`}>
          {filteredContent.length === 0 ? (
            <div className="SellerMyContent__empty">
              <p>No content found matching your criteria.</p>
              <button className="SellerMyContent__emptyBtn">
                <FaPlus />
                <span>Upload Your First Content</span>
              </button>
            </div>
          ) : (
            filteredContent.map((content) => (
              <div key={content.id} className="SellerMyContent__item">
                {/* Thumbnail */}
                <div className="SellerMyContent__thumbnail">
                  {content.thumbnail ? (
                    <img src={content.thumbnail} alt={content.title} />
                  ) : (
                    <div className="SellerMyContent__thumbnailPlaceholder">
                      <span>{content.category}</span>
                    </div>
                  )}
                  <div className={`SellerMyContent__statusBadge ${getStatusBadgeClass(content.status)}`}>
                    {content.status}
                  </div>
                </div>

                {/* Content Info */}
                <div className="SellerMyContent__info">
                  <h3 className="SellerMyContent__title">{content.title}</h3>
                  <p className="SellerMyContent__category">{content.category}</p>
                  <div className="SellerMyContent__meta">
                    <span className="SellerMyContent__price">{formatCurrency(content.price)}</span>
                    <span className="SellerMyContent__sales">{content.sales} sales</span>
                    <span className="SellerMyContent__revenue">{formatCurrency(content.revenue)}</span>
                  </div>
                  <p className="SellerMyContent__date">Uploaded: {formatDate(content.uploadDate)}</p>
                </div>

                {/* Actions */}
                <div className="SellerMyContent__actions">
                  <button className="SellerMyContent__actionBtn SellerMyContent__actionBtn--view" title="View">
                    <FaEye />
                  </button>
                  <button className="SellerMyContent__actionBtn SellerMyContent__actionBtn--edit" title="Edit">
                    <FaEdit />
                  </button>
                  <button className="SellerMyContent__actionBtn SellerMyContent__actionBtn--delete" title="Delete">
                    <FaTrash />
                  </button>
                </div>
              </div>
            ))
          )}
        </div>
      </div>
    </SellerLayout>
  );
};

export default SellerMyContent;
