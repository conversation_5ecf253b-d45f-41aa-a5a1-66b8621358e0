/* SellerLayout Component Styles */
.SellerLayout {
  display: flex;
  min-height: 100vh;
  background-color: var(--bg-gray);
  position: relative;
}

/* Sidebar Section */
.SellerLayout__sidebar {
  width: 280px;
  flex-shrink: 0;
  background-color: var(--white);
  position: relative;
  z-index: var(--z-index-header);
}

/* Main Content Section */
.SellerLayout__main {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 0; /* Prevents flex item from overflowing */
}

/* Header */
.SellerLayout__header {
  background-color: var(--white);
  border-bottom: 1px solid var(--light-gray);
  box-shadow: var(--box-shadow-light);
  position: sticky;
  top: 0;
  z-index: calc(var(--z-index-header) - 1);
}

.SellerLayout__headerContent {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--basefont) var(--heading6);
  max-width: 100%;
}

/* Mobile Toggle Button */
.SellerLayout__toggleBtn {
  display: none;
  background: none;
  border: none;
  font-size: var(--heading5);
  color: var(--secondary-color);
  cursor: pointer;
  padding: var(--extrasmallfont);
  border-radius: var(--border-radius);
  transition: all 0.3s ease;
}

.SellerLayout__toggleBtn:hover {
  background-color: var(--bg-gray);
  color: var(--primary-color);
}

/* Title Section */
.SellerLayout__titleSection {
  flex: 1;
  margin-left: var(--basefont);
}

.SellerLayout__title {
  font-size: var(--heading4);
  font-weight: 600;
  color: var(--secondary-color);
  margin: 0;
  line-height: 1.2;
}

.SellerLayout__subtitle {
  font-size: var(--basefont);
  color: var(--dark-gray);
  margin: var(--extrasmallfont) 0 0 0;
  line-height: 1.4;
}

/* Header Actions */
.SellerLayout__headerActions {
  display: flex;
  align-items: center;
  gap: var(--basefont);
}

/* Content Area */
.SellerLayout__content {
  flex: 1;
  padding: var(--heading6);
  overflow-y: auto;
}

.SellerLayout__contentWrapper {
  max-width: 100%;
  margin: 0 auto;
  background-color: var(--white);
  border-radius: var(--border-radius-large);
  box-shadow: var(--box-shadow-light);
  padding: var(--heading6);
  min-height: calc(100vh - 140px);
}

/* Mobile Overlay */
.SellerLayout__overlay {
  display: none;
}

/* Responsive Design */

/* Large Desktop (≥1200px) */
@media (min-width: 1200px) {
  .SellerLayout__sidebar {
    width: 300px;
  }
  
  .SellerLayout__headerContent {
    padding: var(--heading6) var(--heading4);
  }
  
  .SellerLayout__content {
    padding: var(--heading4);
  }
  
  .SellerLayout__contentWrapper {
    padding: var(--heading4);
  }
}

/* Tablet (768px - 1199px) */
@media (max-width: 1199px) {
  .SellerLayout__sidebar {
    width: 240px;
  }
  
  .SellerLayout__title {
    font-size: var(--heading5);
  }
  
  .SellerLayout__subtitle {
    font-size: var(--smallfont);
  }
  
  .SellerLayout__headerContent {
    padding: var(--smallfont) var(--basefont);
  }
  
  .SellerLayout__content {
    padding: var(--basefont);
  }
  
  .SellerLayout__contentWrapper {
    padding: var(--basefont);
  }
}

/* Mobile (≤767px) */
@media (max-width: 767px) {
  .SellerLayout {
    flex-direction: column;
  }
  
  .SellerLayout__sidebar {
    position: fixed;
    top: 0;
    left: 0;
    width: 280px;
    height: 100vh;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
    z-index: var(--z-index-modal);
  }
  
  .SellerLayout__sidebar--open {
    transform: translateX(0);
  }
  
  .SellerLayout__main {
    width: 100%;
  }
  
  .SellerLayout__toggleBtn {
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .SellerLayout__titleSection {
    margin-left: var(--smallfont);
  }
  
  .SellerLayout__title {
    font-size: var(--heading6);
  }
  
  .SellerLayout__subtitle {
    font-size: var(--extrasmallfont);
  }
  
  .SellerLayout__headerContent {
    padding: var(--smallfont) var(--basefont);
  }
  
  .SellerLayout__content {
    padding: var(--smallfont);
  }
  
  .SellerLayout__contentWrapper {
    padding: var(--basefont);
    border-radius: var(--border-radius);
    min-height: calc(100vh - 120px);
  }
  
  .SellerLayout__overlay {
    display: block;
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: calc(var(--z-index-modal) - 1);
  }
}

/* Small Mobile (≤480px) */
@media (max-width: 480px) {
  .SellerLayout__sidebar {
    width: 260px;
  }
  
  .SellerLayout__title {
    font-size: var(--basefont);
  }
  
  .SellerLayout__subtitle {
    display: none; /* Hide subtitle on very small screens */
  }
  
  .SellerLayout__headerContent {
    padding: var(--extrasmallfont) var(--smallfont);
  }
  
  .SellerLayout__content {
    padding: var(--extrasmallfont);
  }
  
  .SellerLayout__contentWrapper {
    padding: var(--smallfont);
    min-height: calc(100vh - 100px);
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  .SellerLayout__sidebar,
  .SellerLayout__toggleBtn,
  .SellerLayout__overlay {
    transition: none;
  }
}

/* Print Styles */
@media print {
  .SellerLayout__sidebar,
  .SellerLayout__toggleBtn,
  .SellerLayout__overlay {
    display: none;
  }
  
  .SellerLayout__main {
    width: 100%;
  }
  
  .SellerLayout__content {
    padding: 0;
  }
  
  .SellerLayout__contentWrapper {
    box-shadow: none;
    border: 1px solid var(--light-gray);
  }
}
