import { createSlice } from '@reduxjs/toolkit';

// Initial state for the seller dashboard
const initialState = {
  // Sidebar state
  activeTab: 'dashboard', // Default active tab
  isSidebarOpen: false,

  // User profile data (mock data for development)
  profile: {
    firstName: '<PERSON>',
    lastName: '<PERSON>',
    email: 'jane<PERSON>@seller.com',
    phone: '+****************',
    profileImage: null,
    businessName: 'Sports Content Pro',
    businessType: 'Individual Creator',
  },

  // Dashboard statistics
  stats: {
    totalContent: 45,
    totalSales: 1250,
    totalRevenue: 15750.00,
    pendingOrders: 8,
    activeContent: 42,
    draftContent: 3,
    monthlyRevenue: 3200.00,
    monthlyOrders: 156,
  },

  // Recent activities
  recentActivities: [
    {
      id: '1',
      type: 'sale',
      title: 'Basketball Training Video sold',
      amount: 29.99,
      buyer: 'John <PERSON>',
      timestamp: new Date().toISOString(),
    },
    {
      id: '2',
      type: 'upload',
      title: 'New content uploaded: Soccer Tactics',
      timestamp: new Date(Date.now() - 3600000).toISOString(),
    },
    {
      id: '3',
      type: 'review',
      title: 'New 5-star review received',
      content: 'Football Strategy Guide',
      timestamp: new Date(Date.now() - 7200000).toISOString(),
    },
  ],

  // Content management
  myContent: [
    {
      id: '1',
      title: 'Basketball Training Fundamentals',
      category: 'Basketball',
      price: 29.99,
      status: 'active',
      sales: 45,
      revenue: 1349.55,
      uploadDate: '2024-01-15',
      thumbnail: null,
    },
    {
      id: '2',
      title: 'Soccer Tactics Masterclass',
      category: 'Soccer',
      price: 39.99,
      status: 'active',
      sales: 32,
      revenue: 1279.68,
      uploadDate: '2024-01-10',
      thumbnail: null,
    },
    {
      id: '3',
      title: 'Football Strategy Guide',
      category: 'Football',
      price: 24.99,
      status: 'draft',
      sales: 0,
      revenue: 0,
      uploadDate: '2024-01-20',
      thumbnail: null,
    },
  ],

  // Orders and sales
  orders: [
    {
      id: 'ORD-001',
      contentTitle: 'Basketball Training Fundamentals',
      buyer: 'John Doe',
      amount: 29.99,
      status: 'completed',
      orderDate: '2024-01-20',
      paymentMethod: 'Credit Card',
    },
    {
      id: 'ORD-002',
      contentTitle: 'Soccer Tactics Masterclass',
      buyer: 'Sarah Wilson',
      amount: 39.99,
      status: 'pending',
      orderDate: '2024-01-19',
      paymentMethod: 'PayPal',
    },
  ],

  // Analytics data
  analytics: {
    salesChart: {
      labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
      data: [1200, 1900, 3000, 2500, 2200, 3200],
    },
    topContent: [
      { title: 'Basketball Training Fundamentals', sales: 45, revenue: 1349.55 },
      { title: 'Soccer Tactics Masterclass', sales: 32, revenue: 1279.68 },
      { title: 'Tennis Serve Techniques', sales: 28, revenue: 839.72 },
    ],
  },

  // Loading states
  loading: {
    profile: false,
    stats: false,
    content: false,
    orders: false,
    analytics: false,
  },

  // Error states
  errors: {
    profile: null,
    stats: null,
    content: null,
    orders: null,
    analytics: null,
  },
};

const sellerDashboardSlice = createSlice({
  name: 'sellerDashboard',
  initialState,
  reducers: {
    // Sidebar actions
    setActiveTab: (state, action) => {
      state.activeTab = action.payload;
    },
    toggleSidebar: (state) => {
      state.isSidebarOpen = !state.isSidebarOpen;
    },
    setSidebarOpen: (state, action) => {
      state.isSidebarOpen = action.payload;
    },

    // Profile actions
    updateProfile: (state, action) => {
      state.profile = { ...state.profile, ...action.payload };
    },
    setProfileLoading: (state, action) => {
      state.loading.profile = action.payload;
    },
    setProfileError: (state, action) => {
      state.errors.profile = action.payload;
    },

    // Stats actions
    updateStats: (state, action) => {
      state.stats = { ...state.stats, ...action.payload };
    },
    setStatsLoading: (state, action) => {
      state.loading.stats = action.payload;
    },
    setStatsError: (state, action) => {
      state.errors.stats = action.payload;
    },

    // Content actions
    addContent: (state, action) => {
      state.myContent.unshift(action.payload);
    },
    updateContent: (state, action) => {
      const index = state.myContent.findIndex(content => content.id === action.payload.id);
      if (index !== -1) {
        state.myContent[index] = { ...state.myContent[index], ...action.payload };
      }
    },
    deleteContent: (state, action) => {
      state.myContent = state.myContent.filter(content => content.id !== action.payload);
    },
    setContentLoading: (state, action) => {
      state.loading.content = action.payload;
    },
    setContentError: (state, action) => {
      state.errors.content = action.payload;
    },

    // Orders actions
    addOrder: (state, action) => {
      state.orders.unshift(action.payload);
    },
    updateOrder: (state, action) => {
      const index = state.orders.findIndex(order => order.id === action.payload.id);
      if (index !== -1) {
        state.orders[index] = { ...state.orders[index], ...action.payload };
      }
    },
    setOrdersLoading: (state, action) => {
      state.loading.orders = action.payload;
    },
    setOrdersError: (state, action) => {
      state.errors.orders = action.payload;
    },

    // Analytics actions
    updateAnalytics: (state, action) => {
      state.analytics = { ...state.analytics, ...action.payload };
    },
    setAnalyticsLoading: (state, action) => {
      state.loading.analytics = action.payload;
    },
    setAnalyticsError: (state, action) => {
      state.errors.analytics = action.payload;
    },

    // Recent activities actions
    addActivity: (state, action) => {
      state.recentActivities.unshift(action.payload);
      // Keep only the latest 10 activities
      if (state.recentActivities.length > 10) {
        state.recentActivities = state.recentActivities.slice(0, 10);
      }
    },

    // Clear all errors
    clearErrors: (state) => {
      state.errors = {
        profile: null,
        stats: null,
        content: null,
        orders: null,
        analytics: null,
      };
    },

    // Reset dashboard state
    resetDashboard: (state) => {
      return initialState;
    },
  },
});

// Export actions
export const {
  setActiveTab,
  toggleSidebar,
  setSidebarOpen,
  updateProfile,
  setProfileLoading,
  setProfileError,
  updateStats,
  setStatsLoading,
  setStatsError,
  addContent,
  updateContent,
  deleteContent,
  setContentLoading,
  setContentError,
  addOrder,
  updateOrder,
  setOrdersLoading,
  setOrdersError,
  updateAnalytics,
  setAnalyticsLoading,
  setAnalyticsError,
  addActivity,
  clearErrors,
  resetDashboard,
} = sellerDashboardSlice.actions;

// Export selectors
export const selectActiveTab = (state) => state.sellerDashboard.activeTab;
export const selectIsSidebarOpen = (state) => state.sellerDashboard.isSidebarOpen;
export const selectProfile = (state) => state.sellerDashboard.profile;
export const selectStats = (state) => state.sellerDashboard.stats;
export const selectMyContent = (state) => state.sellerDashboard.myContent;
export const selectOrders = (state) => state.sellerDashboard.orders;
export const selectAnalytics = (state) => state.sellerDashboard.analytics;
export const selectRecentActivities = (state) => state.sellerDashboard.recentActivities;
export const selectLoading = (state) => state.sellerDashboard.loading;
export const selectErrors = (state) => state.sellerDashboard.errors;

// Export reducer
export default sellerDashboardSlice.reducer;
