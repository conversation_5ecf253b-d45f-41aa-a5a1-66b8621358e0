/* SellerDashboard Component Styles */
.SellerDashboard {
  display: flex;
  flex-direction: column;
  gap: var(--heading6);
}

/* Stats Grid */
.SellerDashboard__statsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--basefont);
  margin-bottom: var(--heading6);
}

.SellerDashboard__statCard {
  background-color: var(--white);
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius-large);
  padding: var(--heading6);
  display: flex;
  align-items: center;
  gap: var(--basefont);
  box-shadow: var(--box-shadow-light);
  transition: all 0.3s ease;
}

.SellerDashboard__statCard:hover {
  transform: translateY(-2px);
  box-shadow: var(--box-shadow);
}

.SellerDashboard__statIcon {
  width: 48px;
  height: 48px;
  border-radius: var(--border-radius-large);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--heading5);
  color: var(--white);
  flex-shrink: 0;
}

.SellerDashboard__statIcon--revenue {
  background: linear-gradient(135deg, #4CAF50, #45a049);
}

.SellerDashboard__statIcon--sales {
  background: linear-gradient(135deg, #2196F3, #1976D2);
}

.SellerDashboard__statIcon--content {
  background: linear-gradient(135deg, #FF9800, #F57C00);
}

.SellerDashboard__statIcon--orders {
  background: linear-gradient(135deg, var(--primary-color), var(--btn-color));
}

.SellerDashboard__statContent {
  flex: 1;
}

.SellerDashboard__statValue {
  font-size: var(--heading4);
  font-weight: 700;
  color: var(--secondary-color);
  margin: 0 0 var(--extrasmallfont) 0;
  line-height: 1.2;
}

.SellerDashboard__statLabel {
  font-size: var(--basefont);
  color: var(--dark-gray);
  margin: 0 0 var(--extrasmallfont) 0;
  font-weight: 500;
}

.SellerDashboard__statTrend {
  display: flex;
  align-items: center;
  gap: var(--extrasmallfont);
  font-size: var(--smallfont);
  font-weight: 500;
}

.SellerDashboard__statTrend--up {
  color: #4CAF50;
}

.SellerDashboard__statTrend--down {
  color: #f44336;
}

.SellerDashboard__statTrend--neutral {
  color: var(--dark-gray);
}

/* Main Content Grid */
.SellerDashboard__mainGrid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--heading6);
  margin-bottom: var(--heading6);
}

/* Section Styles */
.SellerDashboard__section {
  background-color: var(--white);
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius-large);
  padding: var(--heading6);
  box-shadow: var(--box-shadow-light);
}

.SellerDashboard__sectionHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--basefont);
  padding-bottom: var(--basefont);
  border-bottom: 1px solid var(--light-gray);
}

.SellerDashboard__sectionTitle {
  font-size: var(--heading6);
  font-weight: 600;
  color: var(--secondary-color);
  margin: 0;
}

.SellerDashboard__sectionAction {
  background: none;
  border: none;
  color: var(--primary-color);
  font-size: var(--smallfont);
  font-weight: 500;
  cursor: pointer;
  padding: var(--extrasmallfont) var(--smallfont);
  border-radius: var(--border-radius);
  transition: all 0.3s ease;
}

.SellerDashboard__sectionAction:hover {
  background-color: var(--primary-light-color);
  color: var(--btn-color);
}

/* Activities List */
.SellerDashboard__activitiesList {
  display: flex;
  flex-direction: column;
  gap: var(--basefont);
}

.SellerDashboard__activityItem {
  display: flex;
  align-items: flex-start;
  gap: var(--basefont);
  padding: var(--basefont);
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  transition: all 0.3s ease;
}

.SellerDashboard__activityItem:hover {
  background-color: var(--bg-gray);
  border-color: var(--primary-color);
}

.SellerDashboard__activityIcon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--basefont);
  color: var(--white);
  flex-shrink: 0;
}

.SellerDashboard__activityIcon--sale {
  background-color: #4CAF50;
}

.SellerDashboard__activityIcon--upload {
  background-color: #2196F3;
}

.SellerDashboard__activityIcon--review {
  background-color: #FF9800;
}

.SellerDashboard__activityContent {
  flex: 1;
}

.SellerDashboard__activityTitle {
  font-size: var(--basefont);
  font-weight: 600;
  color: var(--secondary-color);
  margin: 0 0 var(--extrasmallfont) 0;
  line-height: 1.3;
}

.SellerDashboard__activityAmount {
  font-size: var(--smallfont);
  font-weight: 600;
  color: #4CAF50;
  margin: 0;
}

.SellerDashboard__activityBuyer {
  font-size: var(--smallfont);
  color: var(--dark-gray);
  margin: 0;
}

.SellerDashboard__activityTime {
  font-size: var(--extrasmallfont);
  color: var(--dark-gray);
  white-space: nowrap;
  flex-shrink: 0;
}

/* Top Content List */
.SellerDashboard__topContentList {
  display: flex;
  flex-direction: column;
  gap: var(--basefont);
}

.SellerDashboard__topContentItem {
  display: flex;
  align-items: center;
  gap: var(--basefont);
  padding: var(--basefont);
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  transition: all 0.3s ease;
}

.SellerDashboard__topContentItem:hover {
  background-color: var(--bg-gray);
  border-color: var(--primary-color);
}

.SellerDashboard__topContentRank {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: var(--primary-color);
  color: var(--white);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--smallfont);
  font-weight: 600;
  flex-shrink: 0;
}

.SellerDashboard__topContentInfo {
  flex: 1;
}

.SellerDashboard__topContentTitle {
  font-size: var(--basefont);
  font-weight: 600;
  color: var(--secondary-color);
  margin: 0 0 var(--extrasmallfont) 0;
  line-height: 1.3;
}

.SellerDashboard__topContentStats {
  display: flex;
  gap: var(--basefont);
}

.SellerDashboard__topContentSales {
  font-size: var(--smallfont);
  color: var(--dark-gray);
}

.SellerDashboard__topContentRevenue {
  font-size: var(--smallfont);
  font-weight: 600;
  color: #4CAF50;
}

/* Quick Actions */
.SellerDashboard__quickActions {
  background-color: var(--white);
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius-large);
  padding: var(--heading6);
  box-shadow: var(--box-shadow-light);
}

.SellerDashboard__actionGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--basefont);
  margin-top: var(--basefont);
}

.SellerDashboard__actionBtn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--smallfont);
  padding: var(--basefont) var(--heading6);
  border: none;
  border-radius: var(--border-radius);
  font-size: var(--basefont);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
}

.SellerDashboard__actionBtn--primary {
  background-color: var(--primary-color);
  color: var(--white);
}

.SellerDashboard__actionBtn--primary:hover {
  background-color: var(--btn-color);
  transform: translateY(-2px);
  box-shadow: var(--box-shadow);
}

.SellerDashboard__actionBtn--secondary {
  background-color: var(--bg-gray);
  color: var(--secondary-color);
  border: 1px solid var(--light-gray);
}

.SellerDashboard__actionBtn--secondary:hover {
  background-color: var(--primary-light-color);
  color: var(--primary-color);
  border-color: var(--primary-color);
  transform: translateY(-2px);
  box-shadow: var(--box-shadow-light);
}

/* Responsive Design */

/* Large Desktop (≥1200px) */
@media (min-width: 1200px) {
  .SellerDashboard__statsGrid {
    grid-template-columns: repeat(4, 1fr);
  }

  .SellerDashboard__statCard {
    padding: var(--heading4);
  }

  .SellerDashboard__statIcon {
    width: 56px;
    height: 56px;
    font-size: var(--heading4);
  }

  .SellerDashboard__statValue {
    font-size: var(--heading3);
  }
}

/* Tablet (768px - 1199px) */
@media (max-width: 1199px) {
  .SellerDashboard__statsGrid {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--smallfont);
  }

  .SellerDashboard__statCard {
    padding: var(--basefont);
  }

  .SellerDashboard__statIcon {
    width: 40px;
    height: 40px;
    font-size: var(--basefont);
  }

  .SellerDashboard__statValue {
    font-size: var(--heading5);
  }

  .SellerDashboard__mainGrid {
    gap: var(--basefont);
  }

  .SellerDashboard__section {
    padding: var(--basefont);
  }
}

/* Mobile (≤767px) */
@media (max-width: 767px) {
  .SellerDashboard {
    gap: var(--basefont);
  }

  .SellerDashboard__statsGrid {
    grid-template-columns: 1fr;
    gap: var(--smallfont);
    margin-bottom: var(--basefont);
  }

  .SellerDashboard__statCard {
    padding: var(--smallfont);
    gap: var(--smallfont);
  }

  .SellerDashboard__statIcon {
    width: 36px;
    height: 36px;
    font-size: var(--smallfont);
  }

  .SellerDashboard__statValue {
    font-size: var(--heading6);
  }

  .SellerDashboard__statLabel {
    font-size: var(--smallfont);
  }

  .SellerDashboard__statTrend {
    font-size: var(--extrasmallfont);
  }

  .SellerDashboard__mainGrid {
    grid-template-columns: 1fr;
    gap: var(--smallfont);
    margin-bottom: var(--basefont);
  }

  .SellerDashboard__section {
    padding: var(--smallfont);
  }

  .SellerDashboard__sectionTitle {
    font-size: var(--basefont);
  }

  .SellerDashboard__activityItem {
    padding: var(--smallfont);
    gap: var(--smallfont);
  }

  .SellerDashboard__activityIcon {
    width: 28px;
    height: 28px;
    font-size: var(--smallfont);
  }

  .SellerDashboard__activityTitle {
    font-size: var(--smallfont);
  }

  .SellerDashboard__topContentItem {
    padding: var(--smallfont);
    gap: var(--smallfont);
  }

  .SellerDashboard__topContentRank {
    width: 28px;
    height: 28px;
    font-size: var(--extrasmallfont);
  }

  .SellerDashboard__topContentTitle {
    font-size: var(--smallfont);
  }

  .SellerDashboard__quickActions {
    padding: var(--smallfont);
  }

  .SellerDashboard__actionGrid {
    grid-template-columns: 1fr;
    gap: var(--smallfont);
  }

  .SellerDashboard__actionBtn {
    padding: var(--smallfont) var(--basefont);
    font-size: var(--smallfont);
  }
}

/* Small Mobile (≤480px) */
@media (max-width: 480px) {
  .SellerDashboard__statCard {
    flex-direction: column;
    text-align: center;
    gap: var(--extrasmallfont);
  }

  .SellerDashboard__activityItem {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }

  .SellerDashboard__activityTime {
    white-space: normal;
  }

  .SellerDashboard__topContentItem {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }

  .SellerDashboard__topContentStats {
    justify-content: center;
  }
}