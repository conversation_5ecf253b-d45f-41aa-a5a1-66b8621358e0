/* SellerSidebar Component Styles */
.SellerSidebar {
  background-color: var(--white);
  border-right: 1px solid var(--light-gray);
  height: 100%;
  display: flex;
  flex-direction: column;
  position: sticky;
  top: 0;
  box-shadow: var(--box-shadow-light);
}

/* Header Section */
.SellerSidebar__header {
  padding: var(--heading6);
  border-bottom: 1px solid var(--light-gray);
}

.SellerSidebar__logo {
  display: flex;
  align-items: center;
  justify-content: center;
}

.SellerSidebar__logoLink {
  display: flex;
  align-items: center;
  gap: var(--extrasmallfont);
  text-decoration: none;
  color: var(--secondary-color);
  font-weight: 600;
}

.SellerSidebar__logoImage {
  width: 32px;
  height: 32px;
  object-fit: contain;
}

.SellerSidebar__logoText {
  font-size: var(--heading6);
  font-weight: 600;
  color: var(--secondary-color);
}

/* Navigation Section */
.SellerSidebar__nav {
  flex: 1;
  padding: var(--basefont) 0;
  overflow-y: auto;
}

.SellerSidebar__menu {
  list-style: none;
  margin: 0;
  padding: 0;
}

.SellerSidebar__menuItem {
  margin-bottom: var(--extrasmallfont);
}

.SellerSidebar__menuLink {
  display: flex;
  align-items: center;
  gap: var(--smallfont);
  padding: var(--smallfont) var(--heading6);
  color: var(--dark-gray);
  text-decoration: none;
  font-size: var(--basefont);
  font-weight: 500;
  transition: all 0.3s ease;
  border-radius: 0 var(--border-radius-large) var(--border-radius-large) 0;
  margin-right: var(--basefont);
  position: relative;
}

.SellerSidebar__menuLink:hover {
  background-color: var(--primary-light-color);
  color: var(--primary-color);
  transform: translateX(4px);
}

.SellerSidebar__menuLink--active {
  background-color: var(--primary-color);
  color: var(--white);
  font-weight: 600;
}

.SellerSidebar__menuLink--active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background-color: var(--btn-color);
  border-radius: 0 var(--border-radius) var(--border-radius) 0;
}

.SellerSidebar__menuIcon {
  font-size: var(--heading5);
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 24px;
}

.SellerSidebar__menuLabel {
  font-size: var(--basefont);
  white-space: nowrap;
}

/* Footer Section */
.SellerSidebar__footer {
  padding: var(--heading6);
  border-top: 1px solid var(--light-gray);
}

.SellerSidebar__logoutBtn {
  display: flex;
  align-items: center;
  gap: var(--smallfont);
  width: 100%;
  padding: var(--smallfont) var(--basefont);
  background-color: transparent;
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  color: var(--dark-gray);
  font-size: var(--basefont);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.SellerSidebar__logoutBtn:hover {
  background-color: var(--btn-color);
  color: var(--white);
  border-color: var(--btn-color);
  transform: translateY(-2px);
  box-shadow: var(--box-shadow);
}

.SellerSidebar__logoutIcon {
  font-size: var(--heading6);
  display: flex;
  align-items: center;
  justify-content: center;
}

.SellerSidebar__logoutText {
  font-size: var(--basefont);
}

/* Responsive Design */

/* Tablet (768px - 1199px) */
@media (max-width: 1199px) {
  .SellerSidebar {
    width: 240px;
  }
  
  .SellerSidebar__logoText {
    font-size: var(--smallfont);
  }
  
  .SellerSidebar__menuLink {
    padding: var(--extrasmallfont) var(--smallfont);
    gap: var(--extrasmallfont);
  }
  
  .SellerSidebar__menuIcon {
    font-size: var(--heading6);
  }
  
  .SellerSidebar__menuLabel {
    font-size: var(--smallfont);
  }
}

/* Mobile (≤767px) */
@media (max-width: 767px) {
  .SellerSidebar {
    position: fixed;
    top: 0;
    left: 0;
    width: 280px;
    height: 100vh;
    z-index: var(--z-index-modal);
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }
  
  .SellerSidebar--open {
    transform: translateX(0);
  }
  
  .SellerSidebar__header {
    padding: var(--basefont);
  }
  
  .SellerSidebar__logoText {
    font-size: var(--basefont);
  }
  
  .SellerSidebar__menuLink {
    padding: var(--basefont);
    margin-right: 0;
    border-radius: 0;
  }
  
  .SellerSidebar__menuLink:hover {
    transform: none;
  }
  
  .SellerSidebar__footer {
    padding: var(--basefont);
  }
}

/* Small Mobile (≤480px) */
@media (max-width: 480px) {
  .SellerSidebar {
    width: 260px;
  }
  
  .SellerSidebar__logoImage {
    width: 28px;
    height: 28px;
  }
  
  .SellerSidebar__logoText {
    font-size: var(--smallfont);
  }
  
  .SellerSidebar__menuIcon {
    font-size: var(--basefont);
  }
  
  .SellerSidebar__menuLabel {
    font-size: var(--smallfont);
  }
}

/* Sidebar Overlay for Mobile */
@media (max-width: 767px) {
  .SellerSidebar__overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: calc(var(--z-index-modal) - 1);
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
  }
  
  .SellerSidebar__overlay--visible {
    opacity: 1;
    visibility: visible;
  }
}
