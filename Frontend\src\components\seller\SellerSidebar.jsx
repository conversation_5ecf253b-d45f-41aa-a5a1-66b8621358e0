import React from "react";
import { Link, useNavigate } from "react-router-dom";
import { useSelector, useDispatch } from "react-redux";
import {
  selectActiveTab,
  setActiveTab,
} from "../../redux/slices/sellerDashboardSlice";
import { logout } from "../../redux/slices/authSlice";
import "../../styles/SellerSidebar.css";

// Icons
import { MdDashboard } from "react-icons/md";
import { FaUser } from "react-icons/fa";
import { FaUpload } from "react-icons/fa";
import { MdContentPaste } from "react-icons/md";
import { FaChartLine } from "react-icons/fa";
import { FaShoppingCart } from "react-icons/fa";
import { FaCog } from "react-icons/fa";
import { IoLogOut } from "react-icons/io5";

const SellerSidebar = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const activeTab = useSelector(selectActiveTab);

  // Handle tab change
  const handleTabChange = (tab) => {
    dispatch(setActiveTab(tab));
  };

  // Handle logout
  const handleLogout = () => {
    dispatch(logout());
    navigate("/");
  };

  // Sidebar menu items
  const menuItems = [
    {
      id: "dashboard",
      label: "Dashboard",
      icon: <MdDashboard />,
      path: "/seller/dashboard",
    },
    {
      id: "profile",
      label: "Profile",
      icon: <FaUser />,
      path: "/seller/profile",
    },
    {
      id: "upload",
      label: "Upload Content",
      icon: <FaUpload />,
      path: "/seller/upload",
    },
    {
      id: "my-content",
      label: "My Content",
      icon: <MdContentPaste />,
      path: "/seller/my-content",
    },
    {
      id: "analytics",
      label: "Analytics",
      icon: <FaChartLine />,
      path: "/seller/analytics",
    },
    {
      id: "orders",
      label: "Orders & Sales",
      icon: <FaShoppingCart />,
      path: "/seller/orders",
    },
    {
      id: "settings",
      label: "Settings",
      icon: <FaCog />,
      path: "/seller/settings",
    },
  ];

  return (
    <div className="SellerSidebar">
      <div className="SellerSidebar__header">
        <div className="SellerSidebar__logo">
          <Link to="/" className="SellerSidebar__logoLink">
            <img src="/logo.png" alt="XO Sports Hub" className="SellerSidebar__logoImage" />
            <span className="SellerSidebar__logoText">Seller Portal</span>
          </Link>
        </div>
      </div>

      <nav className="SellerSidebar__nav">
        <ul className="SellerSidebar__menu">
          {menuItems.map((item) => (
            <li key={item.id} className="SellerSidebar__menuItem">
              <Link
                to={item.path}
                className={`SellerSidebar__menuLink ${
                  activeTab === item.id ? "SellerSidebar__menuLink--active" : ""
                }`}
                onClick={() => handleTabChange(item.id)}
              >
                <span className="SellerSidebar__menuIcon">{item.icon}</span>
                <span className="SellerSidebar__menuLabel">{item.label}</span>
              </Link>
            </li>
          ))}
        </ul>
      </nav>

      <div className="SellerSidebar__footer">
        <button
          className="SellerSidebar__logoutBtn"
          onClick={handleLogout}
          title="Logout"
        >
          <span className="SellerSidebar__logoutIcon">
            <IoLogOut />
          </span>
          <span className="SellerSidebar__logoutText">Logout</span>
        </button>
      </div>
    </div>
  );
};

export default SellerSidebar;
